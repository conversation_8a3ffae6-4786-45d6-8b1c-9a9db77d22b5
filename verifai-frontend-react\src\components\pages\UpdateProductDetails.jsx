import {
    Box,
    Paper,
    Typography,
    Autocomplete,
    TextField,
    Button,
    Card,
    CardContent,
    Chip,
    LinearProgress,
    Alert,
    Fade,
    Zoom,
    useTheme,
    alpha,
    IconButton,
    Tooltip,
    Container,
    Grid,
    Stack
} from '@mui/material';
import {
    Timeline,
    Update,
    LocationOn,
    Person,
    CalendarToday,
    CheckCircle,
    Cancel,
    Link,
    Security,
    Speed,
    AutoAwesome,
    ArrowBack,
    Fingerprint,
    Schedule
} from '@mui/icons-material';
import { styled, keyframes } from '@mui/material/styles';
import { useEffect, useState } from 'react';
import useAuth from '../../hooks/useAuth';
import { BrowserProvider, Contract } from "ethers";
import { apiGet } from '../../utils/apiUtils';
import dayjs from 'dayjs';
import { useLocation, useNavigate } from 'react-router-dom';
import abi from '../../utils/Verifai.json';
import ParticleField from '../common/ParticleField';


const options = ["true", "false"]

// Futuristic animations
const float = keyframes`
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
`;

const glow = keyframes`
  0%, 100% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.3); }
  50% { box-shadow: 0 0 30px rgba(99, 102, 241, 0.6), 0 0 40px rgba(99, 102, 241, 0.4); }
`;

const shimmer = keyframes`
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
`;

const holographicShift = keyframes`
  0%, 100% {
    background-position: 0% 50%;
    filter: hue-rotate(0deg);
  }
  25% {
    background-position: 100% 50%;
    filter: hue-rotate(90deg);
  }
  50% {
    background-position: 100% 50%;
    filter: hue-rotate(180deg);
  }
  75% {
    background-position: 0% 50%;
    filter: hue-rotate(270deg);
  }
`;

const pulse = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
`;

// Futuristic styled components
const FuturisticContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `
    linear-gradient(135deg,
      ${theme.palette.mode === 'dark' ? '#0a0a0a' : '#f8fafc'} 0%,
      ${theme.palette.mode === 'dark' ? '#1a1a2e' : '#e2e8f0'} 50%,
      ${theme.palette.mode === 'dark' ? '#16213e' : '#cbd5e1'} 100%
    )
  `,
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(217, 70, 239, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 60% 20%, rgba(14, 165, 233, 0.1) 0%, transparent 50%)
    `,
    pointerEvents: 'none',
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      repeating-linear-gradient(
        90deg,
        transparent,
        transparent 98px,
        rgba(99, 102, 241, 0.03) 100px
      ),
      repeating-linear-gradient(
        0deg,
        transparent,
        transparent 98px,
        rgba(16, 185, 129, 0.03) 100px
      )
    `,
    pointerEvents: 'none',
  },
}));

const GlassCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${alpha(theme.palette.background.paper, 0.9)} 0%,
    ${alpha(theme.palette.background.paper, 0.7)} 100%
  )`,
  backdropFilter: 'blur(20px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
  borderRadius: '24px',
  boxShadow: `
    0 8px 32px ${alpha(theme.palette.common.black, 0.1)},
    inset 0 1px 0 ${alpha(theme.palette.common.white, 0.2)}
  `,
  animation: `${float} 6s ease-in-out infinite`,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `
      0 20px 40px ${alpha(theme.palette.common.black, 0.15)},
      0 0 0 1px ${alpha(theme.palette.primary.main, 0.3)}
    `,
  },
}));

const NeonTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.8)} 0%,
      ${alpha(theme.palette.background.paper, 0.6)} 100%
    )`,
    backdropFilter: 'blur(10px)',
    borderRadius: '16px',
    transition: 'all 0.3s ease',
    '& fieldset': {
      border: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
      borderRadius: '16px',
    },
    '&:hover fieldset': {
      border: `2px solid ${alpha(theme.palette.primary.main, 0.5)}`,
      boxShadow: `0 0 20px ${alpha(theme.palette.primary.main, 0.2)}`,
    },
    '&.Mui-focused fieldset': {
      border: `2px solid ${theme.palette.primary.main}`,
      boxShadow: `0 0 25px ${alpha(theme.palette.primary.main, 0.4)}`,
    },
  },
  '& .MuiInputLabel-root': {
    color: theme.palette.text.secondary,
    fontWeight: 600,
    '&.Mui-focused': {
      color: theme.palette.primary.main,
    },
  },
}));

const CyberButton = styled(Button)(({ theme, variant = 'primary' }) => ({
  borderRadius: '16px',
  padding: '12px 32px',
  fontWeight: 700,
  fontSize: '1rem',
  textTransform: 'none',
  position: 'relative',
  overflow: 'hidden',
  background: variant === 'primary'
    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`
    : `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.6)} 100%)`,
  color: variant === 'primary' ? theme.palette.primary.contrastText : theme.palette.text.primary,
  border: variant === 'secondary' ? `2px solid ${alpha(theme.palette.primary.main, 0.3)}` : 'none',
  boxShadow: variant === 'primary'
    ? `0 8px 25px ${alpha(theme.palette.primary.main, 0.3)}`
    : `0 4px 15px ${alpha(theme.palette.common.black, 0.1)}`,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: `linear-gradient(90deg, transparent, ${alpha(theme.palette.common.white, 0.2)}, transparent)`,
    transition: 'left 0.5s',
  },
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: variant === 'primary'
      ? `0 12px 35px ${alpha(theme.palette.primary.main, 0.4)}`
      : `0 8px 25px ${alpha(theme.palette.common.black, 0.15)}`,
    '&::before': {
      left: '100%',
    },
  },
  '&:active': {
    transform: 'translateY(0px)',
  },
}));

const StatusChip = styled(Chip)(({ theme, status }) => ({
  borderRadius: '12px',
  fontWeight: 600,
  padding: '8px 4px',
  background: status === 'success'
    ? `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`
    : status === 'error'
    ? `linear-gradient(135deg, ${theme.palette.error.main} 0%, ${theme.palette.error.dark} 100%)`
    : `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,
  color: theme.palette.common.white,
  boxShadow: `0 4px 15px ${alpha(
    status === 'success' ? theme.palette.success.main :
    status === 'error' ? theme.palette.error.main : theme.palette.warning.main,
    0.3
  )}`,
  animation: status === 'processing' ? `${pulse} 2s ease-in-out infinite` : 'none',
}));

const InfoCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg,
    ${alpha(theme.palette.background.paper, 0.9)} 0%,
    ${alpha(theme.palette.background.paper, 0.7)} 100%
  )`,
  backdropFilter: 'blur(15px)',
  border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
  borderRadius: '20px',
  padding: '24px',
  margin: '16px 0',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 12px 30px ${alpha(theme.palette.common.black, 0.1)}`,
  },
}));

const getEthereumObject = () => window.ethereum;

/*
 * This function returns the first linked account found.
 * If there is no account linked, it will return null.
 */
const findMetaMaskAccount = async () => {
    try {
        const ethereum = getEthereumObject();

        /*
        * First make sure we have access to the Ethereum object.
        */
        if (!ethereum) {
            console.error("Make sure you have Metamask!");
            return null;
        }

        console.log("We have the Ethereum object", ethereum);
        const accounts = await ethereum.request({ method: "eth_accounts" });

        if (accounts.length !== 0) {
            const account = accounts[0];
            console.log("Found an authorized account:", account);
            return account;
        } else {
            console.error("No authorized account found");
            return null;
        }
    } catch (error) {
        console.error(error);
        return null;
    }
};

const UpdateProductDetails = () => {


    const [currDate, setCurrDate] = useState('');
    const [currLatitude, setCurrLatitude] = useState("");
    const [currLongtitude, setCurrLongtitude] = useState("");
    const [currName, setCurrName] = useState("");
    const [currLocation, setCurrLocation] = useState("");
    const [serialNumber, setSerialNumber] = useState("");
    const [isSold, setIsSold] = useState("false"); // Initialize as string to match options
    const [loading, setLoading] = useState("");


    const CONTRACT_ADDRESS = process.env.REACT_APP_CONTRACT_ADDRESS || "0xCC4699e54c5D98b82702305e63835B50e6B5Bfb5";
    const CONTRACT_ABI = abi.abi;

    const { auth, user } = useAuth();
    const navigate = useNavigate();
    const location = useLocation();
    const qrData = location.state?.qrData;


    useEffect(() => {
        console.log("qrdata", qrData)
        const data = qrData.split(",");
        // const contractAddress = data[0];
        setSerialNumber(data[1]);
        console.log("serialNumber", serialNumber)

        findMetaMaskAccount().then((account) => {
            if (account !== null) {
                console.log("MetaMask account found:", account);
            }
        });
    });

    useEffect(() => {
        console.log("useEffect 3")

        // Call getUsername directly without debouncing in useEffect
        getUsername();
        getCurrentTimeLocation();
    }, []);


    useEffect(() => {
        if (currLatitude && currLongtitude) {
            // Use Google Geocoding API directly with fetch
            const geocodeLocation = async () => {
                try {
                    const apiKey = process.env.REACT_APP_GOOGLE_MAPS_API_KEY;
                    if (!apiKey) {
                        console.warn("Google Maps API key not found in environment variables");
                        setCurrLocation(`${currLatitude}, ${currLongtitude}`);
                        return;
                    }

                    const response = await fetch(
                        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${currLatitude},${currLongtitude}&key=${apiKey}`
                    );

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    if (data.status === 'OK' && data.results.length > 0) {
                        const address = data.results[0].formatted_address;
                        setCurrLocation(address.replace(/,/g, ';'));
                        console.log("Geocoded address:", address);
                    } else {
                        console.warn("Geocoding failed:", data.status);
                        setCurrLocation(`${currLatitude}, ${currLongtitude}`);
                    }
                } catch (error) {
                    console.error("Geocoding error:", error);
                    setCurrLocation(`${currLatitude}, ${currLongtitude}`);
                }
            };

            geocodeLocation();
        }
    }, [currLatitude, currLongtitude]);

    const getCurrentTimeLocation = () => {
        setCurrDate(dayjs().unix())
        navigator.geolocation.getCurrentPosition(function (position) {
            setCurrLatitude(position.coords.latitude);
            setCurrLongtitude(position.coords.longitude);
        });
    }


    const getUsername = async () => {
        try {
            // Get the username from the auth object
            const username = auth?.user?.username || user?.username;

            if (!username) {
                console.warn('No username available for profile lookup');
                return;
            }

            const res = await apiGet(`/profile/${username}`);
            console.log(JSON.stringify(res?.data[0]));
            if (res?.data && res.data.length > 0) {
                setCurrName(res.data[0].name);
            }
        } catch (error) {
            console.error("Error fetching username:", error);
            if (error.response?.status === 429) {
                console.warn("Rate limit exceeded. Retrying in a moment...");
                // The apiGet function will handle retries automatically
            }
        }
    }

    const updateProduct = async (e) => {
        e.preventDefault();

        try {
            const { ethereum } = window;

            if (ethereum) {
                // Request account access if needed
                await ethereum.request({ method: 'eth_requestAccounts' });

                const provider = new BrowserProvider(ethereum);
                const signer = await provider.getSigner();
                const productContract = new Contract(CONTRACT_ADDRESS, CONTRACT_ABI, signer);

                console.log("Starting product update transaction...");
                console.log("isSold value:", isSold);
                console.log("isSold type:", typeof isSold);
                console.log("Boolean conversion:", Boolean(isSold === "true"));

                // Check network and prepare optimized transaction options
                const network = await provider.getNetwork();
                const isLocalNetwork = network.chainId === 1337n || network.chainId === 31337n;

                let txOptions = {};
                if (isLocalNetwork) {
                    console.log('🏠 Local network detected, using optimized settings');
                    txOptions = {
                        gasLimit: 400000, // Optimized gas limit for updates
                        gasPrice: BigInt('20000000000'), // 20 gwei
                        type: 0 // Legacy transaction type for speed
                    };
                } else {
                    // For external networks, use conservative settings
                    txOptions = {
                        gasLimit: 350000,
                        gasPrice: BigInt('20000000000')
                    };
                }

                // write transactions with proper gas settings
                const registerTxn = await productContract.addProductHistory(
                    serialNumber,
                    currName,
                    currLocation,
                    currDate.toString(),
                    Boolean(isSold === "true"),
                    txOptions
                );

                console.log("Mining (Adding Product History) ...", registerTxn.hash);
                setLoading(`Mining (Add Product History) ... ${registerTxn.hash}`);

                const receipt = await registerTxn.wait();
                console.log("Mined (Add Product History) --", registerTxn.hash);
                console.log("Transaction receipt:", receipt);

                // Always show success after successful mining, don't try to get product
                console.log("Final isSold value sent to blockchain:", Boolean(isSold === "true"));
                setLoading("✅ Transaction successful! Product history updated on blockchain.");

                // Navigate back to product page to show updated history
                setTimeout(() => {
                    console.log("Navigating back to product page with updated data...");
                    // Add timestamp to force refresh
                    navigate('/product', {
                        state: {
                            qrData,
                            refreshKey: Date.now() // Force component refresh
                        }
                    });
                }, 3000);

            } else {
                console.log("Ethereum object doesn't exist!");
                setLoading("❌ Please install MetaMask to continue.");
            }
        } catch (error) {
            console.error("Error updating product:", error);
            if (error.message.includes("user rejected")) {
                setLoading("❌ Transaction cancelled by user.");
            } else if (error.message.includes("insufficient funds")) {
                setLoading("❌ Insufficient funds for transaction.");
            } else {
                setLoading(`❌ Error: ${error.message}`);
            }

            // Clear error after 5 seconds
            setTimeout(() => {
                setLoading("");
            }, 5000);
        }
    }

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Enhanced role-based validation for supply chain logic
        const userRole = auth?.user?.role || user?.role;

        // Supply Chain Rule: Only retailers can set isSold=true
        if (isSold === "true" && userRole !== "retailer") {
            setLoading("❌ SUPPLY CHAIN RULE: Only retailers can mark products as SOLD. Suppliers and manufacturers can only update location/status.");
            setTimeout(() => setLoading(""), 5000);
            return;
        }

        // Log supply chain action for tracking
        console.log(`Supply Chain Update - Role: ${userRole}, Setting isSold: ${isSold}`);
        if (userRole === "retailer" && isSold === "true") {
            console.log("🛒 RETAILER FINAL SALE: Customer purchase completed");
        } else if (userRole === "retailer" && isSold === "false") {
            console.log("📦 RETAILER ARRIVAL: Product arrived at retail store");
        } else {
            console.log("🚚 SUPPLY CHAIN UPDATE: Location/status update only");
        }

        console.log("Submitting update with role:", userRole, "isSold:", isSold);
        setLoading("Please pay the transaction fee to update the product details...")
        await updateProduct(e);
    }

    const handleBack = () => {
        navigate(-1)
    }


    const theme = useTheme();

    return (
        <FuturisticContainer>
            <ParticleField
                density="medium"
                animated={true}
            />
            <Container maxWidth="md" sx={{ py: 4, position: 'relative', zIndex: 2 }}>
                <Fade in timeout={800}>
                    <GlassCard sx={{ maxWidth: 700, mx: 'auto', p: 5 }}>
                        {/* Header Section */}
                        <Box sx={{ textAlign: 'center', mb: 4 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                                <IconButton
                                    onClick={handleBack}
                                    sx={{
                                        mr: 2,
                                        background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.6)} 100%)`,
                                        backdropFilter: 'blur(10px)',
                                        border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                                        '&:hover': {
                                            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
                                            transform: 'translateY(-2px)',
                                        }
                                    }}
                                >
                                    <ArrowBack />
                                </IconButton>
                                <AutoAwesome sx={{ fontSize: 40, color: theme.palette.primary.main, mr: 2 }} />
                                <Typography
                                    variant="h2"
                                    sx={{
                                        background: `linear-gradient(135deg,
                                            ${theme.palette.primary.main} 0%,
                                            ${theme.palette.secondary.main} 50%,
                                            ${theme.palette.primary.dark} 100%
                                        )`,
                                        backgroundClip: 'text',
                                        WebkitBackgroundClip: 'text',
                                        WebkitTextFillColor: 'transparent',
                                        fontWeight: 700,
                                        fontSize: { xs: '2.5rem', md: '3.5rem' },
                                        letterSpacing: '-0.02em',
                                        fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                                        position: 'relative',
                                        '&::after': {
                                            content: '""',
                                            position: 'absolute',
                                            bottom: '-8px',
                                            left: '50%',
                                            transform: 'translateX(-50%)',
                                            width: '80px',
                                            height: '3px',
                                            background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                                            borderRadius: '2px',
                                        },
                                    }}
                                >
                                    Update Product
                                </Typography>
                            </Box>
                            <Typography
                                variant="body1"
                                sx={{
                                    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                                    color: theme.palette.mode === 'dark' ? '#e2e8f0' : '#475569',
                                    fontWeight: 500,
                                    fontSize: '1.1rem',
                                    letterSpacing: '0.05em',
                                    mb: 2,
                                    position: 'relative',
                                    '&::before': {
                                        content: '">"',
                                        color: theme.palette.primary.main,
                                        marginRight: '8px',
                                        fontWeight: 700,
                                    },
                                }}
                            >
                                Blockchain Enhancement Protocol Active
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500, opacity: 0.8 }}>
                                Secure blockchain journey enhancement system
                            </Typography>
                        </Box>

                        {/* Product Information Cards */}
                        <Stack spacing={3} sx={{ mb: 4 }}>
                            {/* Serial Number Card */}
                            <InfoCard>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                    <Fingerprint sx={{ color: theme.palette.primary.main, mr: 2 }} />
                                    <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                        Product Identity
                                    </Typography>
                                </Box>
                                <NeonTextField
                                    fullWidth
                                    label="Serial Number"
                                    disabled
                                    value={serialNumber}
                                    slotProps={{
                                        input: {
                                            startAdornment: (
                                                <Box sx={{ mr: 1, color: theme.palette.primary.main }}>
                                                    #
                                                </Box>
                                            ),
                                        },
                                    }}
                                />
                            </InfoCard>

                            {/* User Information Card */}
                            <InfoCard>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                    <Person sx={{ color: theme.palette.secondary.main, mr: 2 }} />
                                    <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                        User Information
                                    </Typography>
                                </Box>
                                <Stack spacing={2}>
                                    <NeonTextField
                                        fullWidth
                                        label="Current User"
                                        disabled
                                        value={currName}
                                        slotProps={{
                                            input: {
                                                startAdornment: (
                                                    <Person sx={{ mr: 1, color: theme.palette.secondary.main }} />
                                                ),
                                            },
                                        }}
                                    />
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                            Role:
                                        </Typography>
                                        <Chip
                                            label={auth?.user?.role || user?.role || 'Unknown'}
                                            color={(auth?.user?.role === 'retailer' || user?.role === 'retailer') ? 'primary' : 'default'}
                                            size="small"
                                            sx={{ fontWeight: 600 }}
                                        />
                                        {(auth?.user?.role === 'retailer' || user?.role === 'retailer') && (
                                            <Chip
                                                label="Can Update Sale Status"
                                                color="success"
                                                size="small"
                                                sx={{ fontWeight: 600 }}
                                            />
                                        )}
                                    </Box>
                                </Stack>
                            </InfoCard>

                            {/* Location Card */}
                            <InfoCard>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                    <LocationOn sx={{ color: theme.palette.success.main, mr: 2 }} />
                                    <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                        Current Location
                                    </Typography>
                                </Box>
                                <NeonTextField
                                    fullWidth
                                    label="Geographic Location"
                                    disabled
                                    multiline
                                    rows={2}
                                    value={currLocation.replace(/;/g, ", ")}
                                    slotProps={{
                                        input: {
                                            startAdornment: (
                                                <LocationOn sx={{ mr: 1, color: theme.palette.success.main, alignSelf: 'flex-start', mt: 1 }} />
                                            ),
                                        },
                                    }}
                                />
                            </InfoCard>

                            {/* Timestamp Card */}
                            <InfoCard>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                    <Schedule sx={{ color: theme.palette.warning.main, mr: 2 }} />
                                    <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                        Timestamp
                                    </Typography>
                                </Box>
                                <NeonTextField
                                    fullWidth
                                    label="Update Time"
                                    disabled
                                    value={dayjs(currDate * 1000).format("MMMM D, YYYY h:mm A")}
                                    slotProps={{
                                        input: {
                                            startAdornment: (
                                                <CalendarToday sx={{ mr: 1, color: theme.palette.warning.main }} />
                                            ),
                                        },
                                    }}
                                />
                            </InfoCard>
                        </Stack>

                        {/* Supply Chain Status Section */}
                        <InfoCard>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                <Update sx={{ color: theme.palette.info.main, mr: 2 }} />
                                <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                    Supply Chain Update
                                </Typography>
                            </Box>

                            {/* Supply Chain Role Information */}
                            <Box sx={{ mb: 3, p: 2, borderRadius: 2,
                                background: `linear-gradient(135deg,
                                    ${alpha(theme.palette.info.main, 0.1)} 0%,
                                    ${alpha(theme.palette.info.main, 0.05)} 100%)`,
                                border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
                            }}>
                                <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                                    Your Role: {auth?.user?.role || user?.role || 'Unknown'}
                                </Typography>
                                {(auth?.user?.role === "retailer" || user?.role === "retailer") ? (
                                    <Typography variant="body2" color="text.secondary">
                                        🛒 As a RETAILER, you can update twice:<br/>
                                        1️⃣ Product arrives at shop (Available)<br/>
                                        2️⃣ Customer purchases (Sold) ✅ ONLY YOU CAN SET SOLD
                                    </Typography>
                                ) : (
                                    <Typography variant="body2" color="text.secondary">
                                        🚚 As a {auth?.user?.role || user?.role}, you can only update location/status.<br/>
                                        ❌ You cannot mark products as SOLD (retailer-only)
                                    </Typography>
                                )}
                            </Box>

                            {/* Status Selection - Enhanced for all roles */}
                            {(auth?.user?.role === "retailer" || user?.role === "retailer") && (
                                <Autocomplete
                                    disablePortal={false}
                                    options={options}
                                    fullWidth
                                    value={isSold}
                                    onChange={(_, newVal) => {
                                        console.log("isSold value changed to:", newVal);
                                        setIsSold(newVal);
                                    }}
                                    getOptionLabel={(option) => option === "true" ? "Sold" : "Available"}
                                    isOptionEqualToValue={(option, value) => option === value}
                                    slotProps={{
                                        popper: {
                                            sx: {
                                                '& .MuiAutocomplete-paper': {
                                                    background: `linear-gradient(135deg,
                                                        ${alpha(theme.palette.background.paper, 0.95)} 0%,
                                                        ${alpha(theme.palette.background.paper, 0.9)} 100%
                                                    )`,
                                                    backdropFilter: 'blur(20px)',
                                                    border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                                                    borderRadius: '16px',
                                                    boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
                                                    '& .MuiAutocomplete-option': {
                                                        borderRadius: '8px',
                                                        margin: '4px 8px',
                                                        '&:hover': {
                                                            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
                                                        },
                                                        '&[aria-selected="true"]': {
                                                            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.2)} 0%, ${alpha(theme.palette.secondary.main, 0.2)} 100%)`,
                                                        },
                                                    },
                                                },
                                                zIndex: 9999,
                                            }
                                        }
                                    }}
                                    renderInput={(params) => (
                                        <NeonTextField
                                            {...params}
                                            label="Sale Status"
                                            slotProps={{
                                                input: {
                                                    ...params.InputProps,
                                                    startAdornment: (
                                                        <Box sx={{ mr: 1, color: theme.palette.info.main }}>
                                                            {isSold === "true" ? <CheckCircle /> : <Cancel />}
                                                        </Box>
                                                    ),
                                                },
                                            }}
                                        />
                                    )}
                                />
                            )}
                        </InfoCard>

                        {/* Loading Status */}
                        {loading && (
                            <Zoom in timeout={500}>
                                <InfoCard sx={{ textAlign: 'center' }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                                        <Link sx={{ color: theme.palette.primary.main, mr: 2 }} />
                                        <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                            Blockchain Transaction
                                        </Typography>
                                    </Box>
                                    <StatusChip
                                        label={loading}
                                        status={loading.includes("❌") ? "error" : loading.includes("✅") ? "success" : "processing"}
                                        icon={loading.includes("Mining") ? <Speed /> : loading.includes("✅") ? <CheckCircle /> : <Security />}
                                    />
                                    {loading.includes("Mining") && (
                                        <LinearProgress
                                            sx={{
                                                mt: 2,
                                                borderRadius: 2,
                                                height: 8,
                                                background: alpha(theme.palette.primary.main, 0.1),
                                                '& .MuiLinearProgress-bar': {
                                                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                                                    borderRadius: 2,
                                                }
                                            }}
                                        />
                                    )}
                                </InfoCard>
                            </Zoom>
                        )}

                        {/* Action Buttons */}
                        <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', mt: 5 }}>
                            <CyberButton
                                variant="primary"
                                onClick={handleSubmit}
                                disabled={!!loading && !loading.includes("✅") && !loading.includes("❌")}
                                startIcon={<Update />}
                                sx={{
                                    minWidth: 220,
                                    py: 2,
                                    fontSize: '1.1rem',
                                    fontWeight: 600,
                                    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                                    textTransform: 'none',
                                    borderRadius: '12px',
                                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                                    boxShadow: `0 8px 24px ${alpha(theme.palette.primary.main, 0.3)}`,
                                    '&:hover': {
                                        transform: 'translateY(-2px)',
                                        boxShadow: `0 12px 32px ${alpha(theme.palette.primary.main, 0.4)}`,
                                    }
                                }}
                            >
                                {loading && !loading.includes("✅") && !loading.includes("❌") ? "Processing..." : "Update Product"}
                            </CyberButton>
                        </Box>

                    </GlassCard>
                </Fade>
            </Container>
        </FuturisticContainer>
    )
}

export default UpdateProductDetails;