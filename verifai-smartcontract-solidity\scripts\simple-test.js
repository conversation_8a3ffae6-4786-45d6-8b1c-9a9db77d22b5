const hre = require("hardhat");

async function main() {
    console.log("🔍 Simple contract test...\n");

    // Contract address
    const contractAddress = "******************************************";
    
    console.log("📋 Contract Address:", contractAddress);
    
    try {
        // Get the contract factory
        const VerifaiFactory = await hre.ethers.getContractFactory("VerifaiDebug");
        
        // Connect to the deployed contract
        const contract = VerifaiFactory.attach(contractAddress);
        
        console.log("✅ Connected to contract successfully\n");
        
        // Test product data
        const serialNumber = "debug123";
        
        console.log("📦 Step 1: Check if product exists...");

        try {
            // First check if the product exists using checkProduct
            const checkResult = await contract.checkProduct(serialNumber);
            console.log("✅ Product check result:");
            console.log("   Exists:", checkResult[0]);
            console.log("   Stored Serial:", checkResult[1]);
            console.log("   History Size:", checkResult[2].toString());

            if (!checkResult[0]) {
                console.log("📦 Product doesn't exist, registering new one...");
                const registerTx = await contract.registerProduct(
                    "Test Product",
                    "Test Brand",
                    serialNumber,
                    "Test Description",
                    "test.jpg",
                    "Test Manufacturer",
                    "Test Location",
                    Math.floor(Date.now() / 1000).toString()
                );

                console.log("⏳ Registration transaction:", registerTx.hash);
                await registerTx.wait();
                console.log("✅ Product registered!");
            } else {
                console.log("✅ Product already exists!");
            }

        } catch (error) {
            console.log("❌ Check/Registration failed:", error.message);
            return;
        }
        
        console.log("\n📦 Step 2: Try to retrieve the product...");
        
        try {
            const product = await contract.getProduct(serialNumber);
            console.log("✅ Product retrieved successfully!");
            console.log("📋 Product details:");
            console.log("   Serial:", product[0]);
            console.log("   Name:", product[1]);
            console.log("   Brand:", product[2]);
            console.log("   Description:", product[3]);
            console.log("   Image:", product[4]);
            console.log("   History length:", product[5].length);
            
            if (product[5].length > 0) {
                console.log("   First history entry:");
                console.log("     Actor:", product[5][0].actor);
                console.log("     Location:", product[5][0].location);
                console.log("     Timestamp:", product[5][0].timestamp);
                console.log("     Is Sold:", product[5][0].isSold);
            }
            
        } catch (error) {
            console.log("❌ Retrieval failed:", error.message);
            console.log("This suggests the product wasn't actually saved properly");
            return;
        }
        
        console.log("\n📦 Step 3: Add supply chain update...");
        
        try {
            const updateTx = await contract.addProductHistory(
                serialNumber,
                "Test Supplier",
                "Supplier Location",
                (Math.floor(Date.now() / 1000) + 3600).toString(),
                false
            );
            
            console.log("⏳ Update transaction:", updateTx.hash);
            await updateTx.wait();
            console.log("✅ History updated!");
            
        } catch (error) {
            console.log("❌ Update failed:", error.message);
            return;
        }
        
        console.log("\n📦 Step 4: Retrieve updated product...");
        
        try {
            const updatedProduct = await contract.getProduct(serialNumber);
            console.log("✅ Updated product retrieved!");
            console.log("📋 Updated history length:", updatedProduct[5].length);
            
            for (let i = 0; i < updatedProduct[5].length; i++) {
                console.log(`   History ${i + 1}:`);
                console.log(`     Actor: ${updatedProduct[5][i].actor}`);
                console.log(`     Location: ${updatedProduct[5][i].location}`);
                console.log(`     Is Sold: ${updatedProduct[5][i].isSold}`);
            }
            
            console.log("\n🎉 Test completed successfully!");
            console.log(`🔗 QR Code: ${contractAddress},${serialNumber}`);
            
        } catch (error) {
            console.log("❌ Final retrieval failed:", error.message);
        }
        
    } catch (error) {
        console.log("❌ Error:", error.message);
    }
}

// Execute the test
if (require.main === module) {
    main()
        .then(() => {
            process.exit(0);
        })
        .catch((error) => {
            console.error("❌ Test failed:", error);
            process.exit(1);
        });
}

module.exports = main;
