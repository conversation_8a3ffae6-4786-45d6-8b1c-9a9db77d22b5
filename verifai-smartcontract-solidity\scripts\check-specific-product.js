const hre = require("hardhat");

async function main() {
    console.log("🔍 Checking specific product in blockchain...\n");

    // Contract address
    const contractAddress = "******************************************";
    
    console.log("📋 Contract Address:", contractAddress);
    
    try {
        // Get the contract factory
        const VerifaiFactory = await hre.ethers.getContractFactory("VerifaiDebug");
        
        // Connect to the deployed contract
        const contract = VerifaiFactory.attach(contractAddress);
        
        console.log("✅ Connected to contract successfully\n");
        
        // Check the specific product from your QR code
        const serialNumber = "debug123";
        
        console.log(`🔍 Checking product with serial number: "${serialNumber}"`);
        
        try {
            const product = await contract.getProduct(serialNumber);
            
            console.log("✅ Product found!");
            console.log("📦 Product Details:");
            console.log(`   Name: ${product.name}`);
            console.log(`   Brand: ${product.brand}`);
            console.log(`   Serial Number: ${product.serialNumber}`);
            console.log(`   Description: ${product.description}`);
            console.log(`   Image: ${product.image}`);
            console.log(`   Manufacturer: ${product.manufacturerName}`);
            console.log(`   Location: ${product.manufacturerLocation}`);
            console.log(`   Date: ${new Date(product.manufacturerDate * 1000).toLocaleDateString()}`);
            console.log(`   🔗 QR Code: ${contractAddress},${serialNumber}`);
            
            console.log("\n🎉 This product is AUTHENTIC!");
            console.log("✅ Your QR scanner should route to authentic-product page");
            
        } catch (error) {
            console.log(`❌ Product not found for serial number: "${serialNumber}"`);
            console.log("Error:", error.message);
            
            console.log("\n💡 This means:");
            console.log("1. The product hasn't been registered yet");
            console.log("2. The serial number might be incorrect");
            console.log("3. The product might be counterfeit");
            
            console.log("\n🔧 To fix this:");
            console.log("1. Register a product with serial number 'dffg'");
            console.log("2. Or use a different QR code with an existing product");
        }
        
    } catch (error) {
        console.log("❌ Error:", error.message);
    }
}

// Execute the check
if (require.main === module) {
    main()
        .then(() => {
            process.exit(0);
        })
        .catch((error) => {
            console.error("❌ Script failed:", error);
            process.exit(1);
        });
}

module.exports = main;
