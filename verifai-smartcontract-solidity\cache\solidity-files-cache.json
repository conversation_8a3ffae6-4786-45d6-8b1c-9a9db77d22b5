{"_format": "hh-sol-cache-2", "files": {"C:\\Users\\<USER>\\Documents\\GitHub\\anti-counterfeitproduct\\Verifai\\verifai-smartcontract-solidity\\contracts\\Verifai.sol": {"lastModificationDate": 1750045255345, "contentHash": "a352aeb37ab3143af399222b9d6e6637", "sourceName": "contracts/Verifai.sol", "solcConfig": {"version": "0.8.17", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["hardhat/console.sol"], "versionPragmas": ["^0.8.17"], "artifacts": ["Verifa<PERSON>"]}, "C:\\Users\\<USER>\\Documents\\GitHub\\anti-counterfeitproduct\\Verifai\\verifai-smartcontract-solidity\\node_modules\\hardhat\\console.sol": {"lastModificationDate": 1748006015436, "contentHash": "681c532e816169606d13a5fe8b475074", "sourceName": "hardhat/console.sol", "solcConfig": {"version": "0.8.17", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.4.22 <0.9.0"], "artifacts": ["console"]}, "C:\\Users\\<USER>\\Documents\\GitHub\\anti-counterfeitproduct\\Verifai\\verifai-smartcontract-solidity\\contracts\\VerifaiDebug.sol": {"lastModificationDate": 1749997547280, "contentHash": "05b892163c14a4ebcb5482e2f2ca7308", "sourceName": "contracts/VerifaiDebug.sol", "solcConfig": {"version": "0.8.17", "settings": {"optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["hardhat/console.sol"], "versionPragmas": ["^0.8.17"], "artifacts": ["VerifaiDebug"]}}}